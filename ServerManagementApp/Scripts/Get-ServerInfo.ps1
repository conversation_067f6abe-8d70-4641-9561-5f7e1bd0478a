param(
    [Parameter(Mandatory=$true)]
    [string]$ComputerName,

    [Parameter(Mandatory=$false)]
    [string]$Username,

    [Parameter(Mandatory=$false)]
    [string]$Password
)

try {
    # Create credential object if username and password provided
    $credential = $null
    if ($Username -and $Password) {
        $securePassword = ConvertTo-SecureString $Password -AsPlainText -Force
        $credential = New-Object System.Management.Automation.PSCredential($Username, $securePassword)
    }

    # For localhost, run directly without remote session
    if ($ComputerName -eq "localhost" -or $ComputerName -eq "127.0.0.1" -or $ComputerName -eq $env:COMPUTERNAME) {

        # CPU Usage - Get average over multiple samples for accuracy
        $cpuSamples = @()
        for ($i = 0; $i -lt 3; $i++) {
            $cpu = Get-CimInstance -ClassName Win32_Processor
            $cpuSamples += ($cpu | Measure-Object -Property LoadPercentage -Average).Average
            if ($i -lt 2) { Start-Sleep -Seconds 1 }
        }
        $cpuUsage = [math]::Round(($cpuSamples | Measure-Object -Average).Average, 0)

        # Memory Information using CIM (more reliable than WMI)
        $memory = Get-CimInstance -ClassName Win32_OperatingSystem
        $totalMemoryGB = [math]::Round($memory.TotalVisibleMemorySize / 1MB, 2)
        $freeMemoryGB = [math]::Round($memory.FreePhysicalMemory / 1MB, 2)
        $usedMemoryGB = [math]::Round($totalMemoryGB - $freeMemoryGB, 2)

        # Operating System Information
        $osName = $memory.Caption
        $lastBootTime = $memory.LastBootUpTime

        # Disk Information using CIM for better accuracy
        $diskInfo = Get-CimInstance -ClassName Win32_LogicalDisk -Filter "DriveType=3" | ForEach-Object {
            $totalBytes = $_.Size
            $freeBytes = $_.FreeSpace
            $usedBytes = $totalBytes - $freeBytes

            $totalGB = [math]::Round($totalBytes / 1GB, 2)
            $freeGB = [math]::Round($freeBytes / 1GB, 2)
            $usedGB = [math]::Round($usedBytes / 1GB, 2)
            $usagePercent = if ($totalBytes -gt 0) { [math]::Round(($usedBytes / $totalBytes) * 100, 1) } else { 0 }

            [PSCustomObject]@{
                DriveLetter = $_.DeviceID.Replace(':', '')
                Label = if ([string]::IsNullOrEmpty($_.VolumeName)) { "Local Disk" } else { $_.VolumeName }
                TotalSpaceGB = $totalGB
                UsedSpaceGB = $usedGB
                FreeSpaceGB = $freeGB
                UsagePercent = $usagePercent
            }
        }

        # Pending Updates Check
        $pendingUpdates = $false
        try {
            if (Get-Module -ListAvailable -Name PSWindowsUpdate) {
                $updates = Get-WUList -MicrosoftUpdate
                $pendingUpdates = $updates.Count -gt 0
            } else {
                # Alternative method using Windows Update COM object
                $updateSession = New-Object -ComObject Microsoft.Update.Session
                $updateSearcher = $updateSession.CreateUpdateSearcher()
                $searchResult = $updateSearcher.Search("IsInstalled=0 and Type='Software'")
                $pendingUpdates = $searchResult.Updates.Count -gt 0
            }
        } catch {
            $pendingUpdates = $false
        }

        # Pending Reboot Check
        $pendingReboot = $false
        try {
            $rebootRequired = @(
                "HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\WindowsUpdate\Auto Update\RebootRequired",
                "HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\WindowsUpdate\Auto Update\PostRebootReporting",
                "HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\Component Based Servicing\RebootPending",
                "HKLM:\SOFTWARE\Microsoft\ServerManager\CurrentRebootAttempts"
            )

            foreach ($path in $rebootRequired) {
                if (Test-Path $path) {
                    $pendingReboot = $true
                    break
                }
            }

            # Check for pending file rename operations
            $pendingFileRename = Get-ItemProperty "HKLM:\SYSTEM\CurrentControlSet\Control\Session Manager" -Name "PendingFileRenameOperations" -ErrorAction SilentlyContinue
            if ($pendingFileRename) {
                $pendingReboot = $true
            }
        } catch {
            $pendingReboot = $false
        }

        # Return structured data
        $result = [PSCustomObject]@{
            ComputerName = $env:COMPUTERNAME
            CpuUsage = [int]$cpuUsage
            TotalMemoryGB = $totalMemoryGB
            UsedMemoryGB = $usedMemoryGB
            FreeMemoryGB = $freeMemoryGB
            OperatingSystem = $osName
            LastBootTime = $lastBootTime
            DiskPartitions = $diskInfo
            HasPendingUpdates = $pendingUpdates
            RequiresReboot = $pendingReboot
            Timestamp = Get-Date
        }

    } else {
        # For remote computers
        $sessionParams = @{
            ComputerName = $ComputerName
            ErrorAction = 'Stop'
        }

        if ($credential) {
            $sessionParams.Credential = $credential
        }

        # Test connection first
        if (-not (Test-Connection -ComputerName $ComputerName -Count 1 -Quiet)) {
            throw "Server $ComputerName ist nicht erreichbar"
        }

        # Try different authentication methods for domain environments
        $authMethods = @('Default', 'Negotiate', 'Kerberos', 'Basic')
        $sessionCreated = $false
        $lastError = $null

        foreach ($authMethod in $authMethods) {
            try {
                $testParams = @{
                    ComputerName = $ComputerName
                }

                if ($credential) {
                    $testParams.Credential = $credential
                }

                if ($authMethod -ne 'Default') {
                    $testParams.Authentication = $authMethod
                }

                # Test session creation
                $testSession = New-PSSession @testParams -ErrorAction Stop
                if ($testSession) {
                    Remove-PSSession $testSession -ErrorAction SilentlyContinue
                    $sessionParams = $testParams
                    $sessionCreated = $true
                    break
                }
            } catch {
                $lastError = $_.Exception.Message
                continue
            }
        }

        if (-not $sessionCreated) {
            throw "Keine erfolgreiche Authentifizierung möglich. Letzter Fehler: $lastError"
        }

        $result = Invoke-Command @sessionParams -ScriptBlock {
            # Same logic as above but in remote session
            # CPU Usage with multiple samples
            $cpuSamples = @()
            for ($i = 0; $i -lt 3; $i++) {
                $cpu = Get-CimInstance -ClassName Win32_Processor
                $cpuSamples += ($cpu | Measure-Object -Property LoadPercentage -Average).Average
                if ($i -lt 2) { Start-Sleep -Seconds 1 }
            }
            $cpuUsage = [math]::Round(($cpuSamples | Measure-Object -Average).Average, 0)

            # Memory Information
            $memory = Get-CimInstance -ClassName Win32_OperatingSystem
            $totalMemoryGB = [math]::Round($memory.TotalVisibleMemorySize / 1MB, 2)
            $freeMemoryGB = [math]::Round($memory.FreePhysicalMemory / 1MB, 2)
            $usedMemoryGB = [math]::Round($totalMemoryGB - $freeMemoryGB, 2)

            # Operating System Information
            $osName = $memory.Caption
            $lastBootTime = $memory.LastBootUpTime

            # Disk Information
            $diskInfo = Get-CimInstance -ClassName Win32_LogicalDisk -Filter "DriveType=3" | ForEach-Object {
                $totalBytes = $_.Size
                $freeBytes = $_.FreeSpace
                $usedBytes = $totalBytes - $freeBytes

                $totalGB = [math]::Round($totalBytes / 1GB, 2)
                $freeGB = [math]::Round($freeBytes / 1GB, 2)
                $usedGB = [math]::Round($usedBytes / 1GB, 2)
                $usagePercent = if ($totalBytes -gt 0) { [math]::Round(($usedBytes / $totalBytes) * 100, 1) } else { 0 }

                [PSCustomObject]@{
                    DriveLetter = $_.DeviceID.Replace(':', '')
                    Label = if ([string]::IsNullOrEmpty($_.VolumeName)) { "Local Disk" } else { $_.VolumeName }
                    TotalSpaceGB = $totalGB
                    UsedSpaceGB = $usedGB
                    FreeSpaceGB = $freeGB
                    UsagePercent = $usagePercent
                }
            }

            # Return structured data
            [PSCustomObject]@{
                ComputerName = $env:COMPUTERNAME
                CpuUsage = [int]$cpuUsage
                TotalMemoryGB = $totalMemoryGB
                UsedMemoryGB = $usedMemoryGB
                FreeMemoryGB = $freeMemoryGB
                OperatingSystem = $osName
                LastBootTime = $lastBootTime
                DiskPartitions = $diskInfo
                HasPendingUpdates = $false
                RequiresReboot = $false
                Timestamp = Get-Date
            }
        }
    }

    # Convert to JSON for easy parsing in C#
    $result | ConvertTo-Json -Depth 3

} catch {
    # Return error information as JSON
    $errorInfo = [PSCustomObject]@{
        Error = $true
        ErrorMessage = $_.Exception.Message
        ComputerName = $ComputerName
        Timestamp = Get-Date
    }

    $errorInfo | ConvertTo-Json -Depth 2
}
