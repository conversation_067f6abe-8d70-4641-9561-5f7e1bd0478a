# Configure TrustedHosts for HAUG domain servers
# This script must be run as Administrator

param(
    [Parameter(Mandatory=$false)]
    [string[]]$ServerIPs = @("*************", "*************", "*************", "************", "************", "*************", "*************")
)

try {
    # Check if running as Administrator
    $currentPrincipal = New-Object Security.Principal.WindowsPrincipal([Security.Principal.WindowsIdentity]::GetCurrent())
    if (-not $currentPrincipal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)) {
        throw "Dieses Skript muss als Administrator ausgeführt werden."
    }

    Write-Host "Konfiguriere TrustedHosts für HAUG-Domänen-Server..." -ForegroundColor Green

    # Get current TrustedHosts
    $currentTrustedHosts = (Get-Item WSMan:\localhost\Client\TrustedHosts).Value
    Write-Host "Aktuelle TrustedHosts: $currentTrustedHosts"

    # Build new TrustedHosts list
    $newTrustedHosts = @()
    
    # Add existing hosts if any
    if ($currentTrustedHosts -and $currentTrustedHosts -ne "") {
        $newTrustedHosts += $currentTrustedHosts.Split(',') | ForEach-Object { $_.Trim() }
    }

    # Add server IPs if not already present
    foreach ($ip in $ServerIPs) {
        if ($newTrustedHosts -notcontains $ip) {
            $newTrustedHosts += $ip
            Write-Host "Hinzugefügt: $ip" -ForegroundColor Yellow
        } else {
            Write-Host "Bereits vorhanden: $ip" -ForegroundColor Gray
        }
    }

    # Set new TrustedHosts
    $trustedHostsString = $newTrustedHosts -join ','
    Set-Item WSMan:\localhost\Client\TrustedHosts -Value $trustedHostsString -Force

    Write-Host "TrustedHosts erfolgreich konfiguriert: $trustedHostsString" -ForegroundColor Green

    # Verify configuration
    $verifyTrustedHosts = (Get-Item WSMan:\localhost\Client\TrustedHosts).Value
    Write-Host "Verifizierung - Neue TrustedHosts: $verifyTrustedHosts" -ForegroundColor Cyan

    # Test WinRM connectivity to first server
    if ($ServerIPs.Count -gt 0) {
        $testServer = $ServerIPs[0]
        Write-Host "Teste WinRM-Verbindung zu $testServer..." -ForegroundColor Yellow
        
        try {
            Test-WSMan -ComputerName $testServer -ErrorAction Stop
            Write-Host "✅ WinRM-Verbindung zu $testServer erfolgreich!" -ForegroundColor Green
        } catch {
            Write-Host "❌ WinRM-Verbindung zu $testServer fehlgeschlagen: $($_.Exception.Message)" -ForegroundColor Red
        }
    }

} catch {
    Write-Host "❌ Fehler beim Konfigurieren der TrustedHosts: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}
