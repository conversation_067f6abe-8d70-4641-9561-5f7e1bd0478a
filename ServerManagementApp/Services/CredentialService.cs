using System;
using System.Security;
using System.Windows;

namespace ServerManagementApp.Services
{
    public class CredentialService
    {
        private static CredentialService _instance;
        private string _username;
        private SecureString _password;
        private bool _hasCredentials;

        public static CredentialService Instance => _instance ??= new CredentialService();

        private CredentialService() { }

        public bool HasCredentials => _hasCredentials;
        public string Username => _username;
        public SecureString Password => _password;

        public bool RequestCredentials()
        {
            try
            {
                var credentialDialog = new CredentialDialog();
                var result = credentialDialog.ShowDialog();

                if (result == true)
                {
                    // Automatisch HAUG-Domäne hinzufügen, wenn nicht bereits vorhanden
                    var username = credentialDialog.Username;
                    if (!username.Contains("\\") && !username.Contains("@"))
                    {
                        username = $"HAUG\\{username}";
                    }

                    _username = username;
                    _password = credentialDialog.Password;
                    _hasCredentials = true;
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Fehler beim Abfragen der Anmeldedaten: {ex.Message}", 
                    "Credential-Fehler", MessageBoxButton.OK, MessageBoxImage.Error);
                return false;
            }
        }

        public void ClearCredentials()
        {
            _username = null;
            _password?.Dispose();
            _password = null;
            _hasCredentials = false;
        }

        public object GetPSCredential()
        {
            if (!_hasCredentials)
                return null;

            // Return credential data that can be used by PowerShell
            return new { Username = _username, Password = _password };
        }
    }
}
