using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Text.Json;
using System.Threading.Tasks;
using ServerManagementApp.Models;

namespace ServerManagementApp.Services
{
    public class PowerShellService
    {
        private readonly string _scriptPath;

        public PowerShellService()
        {
            _scriptPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Scripts", "Get-ServerInfo.ps1");
        }

        public async Task<SystemInfo> GetSystemInfoAsync(string computerName = "localhost")
        {
            try
            {
                // Use the new PowerShell script for accurate data
                if (File.Exists(_scriptPath))
                {
                    var serverInfo = await GetServerInfoWithScriptAsync(computerName);

                    var systemInfo = new SystemInfo
                    {
                        CpuUsage = serverInfo.CpuUsage,
                        TotalMemoryGB = serverInfo.TotalMemoryGB,
                        UsedMemoryGB = serverInfo.UsedMemoryGB
                    };

                    foreach (var partition in serverInfo.DiskPartitions)
                    {
                        systemInfo.DiskPartitions.Add(partition);
                    }

                    return systemInfo;
                }
                else
                {
                    // Fallback to old method if script doesn't exist
                    return await GetSystemInfoLegacyAsync(computerName);
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Fehler beim Abrufen der Systemdaten für {computerName}: {ex.Message}");
                // Fallback to legacy method
                return await GetSystemInfoLegacyAsync(computerName);
            }
        }

        private async Task<ServerSystemInfo> GetServerInfoWithScriptAsync(string computerName)
        {
            try
            {
                var arguments = $"-ExecutionPolicy Bypass -File \"{_scriptPath}\" -ComputerName \"{computerName}\"";

                // Add credentials if available
                var credentialService = CredentialService.Instance;
                if (credentialService.HasCredentials)
                {
                    var username = credentialService.Username;
                    var password = ConvertSecureStringToString(credentialService.Password);
                    arguments += $" -Username \"{username}\" -Password \"{password}\"";
                }

                var startInfo = new ProcessStartInfo
                {
                    FileName = "powershell.exe",
                    Arguments = arguments,
                    UseShellExecute = false,
                    RedirectStandardOutput = true,
                    RedirectStandardError = true,
                    CreateNoWindow = true,
                    WorkingDirectory = Path.GetDirectoryName(_scriptPath)
                };

                Debug.WriteLine($"Führe PowerShell-Skript aus für {computerName}: {startInfo.Arguments}");

                using var process = new Process { StartInfo = startInfo };
                process.Start();

                var output = await process.StandardOutput.ReadToEndAsync();
                var error = await process.StandardError.ReadToEndAsync();

                await process.WaitForExitAsync();

                if (process.ExitCode != 0)
                {
                    throw new Exception($"PowerShell-Skript beendet mit Exit-Code {process.ExitCode}: {error}");
                }

                if (string.IsNullOrWhiteSpace(output))
                {
                    throw new Exception("Keine Daten vom PowerShell-Skript erhalten");
                }

                // Clean up output - remove any non-JSON content
                var jsonStart = output.IndexOf('{');
                var jsonEnd = output.LastIndexOf('}');

                if (jsonStart == -1 || jsonEnd == -1)
                {
                    throw new Exception($"Ungültiges JSON vom PowerShell-Skript: {output}");
                }

                var jsonContent = output.Substring(jsonStart, jsonEnd - jsonStart + 1);

                // Parse JSON response
                var jsonOptions = new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                };

                var result = JsonSerializer.Deserialize<PowerShellResult>(jsonContent, jsonOptions);

                if (result.Error)
                {
                    throw new Exception($"Server-Fehler: {result.ErrorMessage}");
                }

                return new ServerSystemInfo
                {
                    ComputerName = result.ComputerName,
                    CpuUsage = result.CpuUsage,
                    TotalMemoryGB = result.TotalMemoryGB,
                    UsedMemoryGB = result.UsedMemoryGB,
                    OperatingSystem = result.OperatingSystem,
                    LastBootTime = result.LastBootTime,
                    DiskPartitions = ConvertDiskPartitions(result.DiskPartitions),
                    HasPendingUpdates = result.HasPendingUpdates,
                    RequiresReboot = result.RequiresReboot,
                    Timestamp = result.Timestamp
                };
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Fehler beim Abrufen der Serverdaten für {computerName}: {ex.Message}");
                throw;
            }
        }

        private List<DiskPartition> ConvertDiskPartitions(List<PowerShellDiskInfo> diskInfos)
        {
            var partitions = new List<DiskPartition>();

            if (diskInfos != null)
            {
                foreach (var diskInfo in diskInfos)
                {
                    partitions.Add(new DiskPartition
                    {
                        DriveLetter = diskInfo.DriveLetter,
                        Label = diskInfo.Label,
                        TotalSpaceGB = diskInfo.TotalSpaceGB,
                        UsedSpaceGB = diskInfo.UsedSpaceGB
                    });
                }
            }

            return partitions;
        }

        private string ConvertSecureStringToString(System.Security.SecureString secureString)
        {
            if (secureString == null)
                return string.Empty;

            IntPtr ptr = System.Runtime.InteropServices.Marshal.SecureStringToBSTR(secureString);
            try
            {
                return System.Runtime.InteropServices.Marshal.PtrToStringBSTR(ptr);
            }
            finally
            {
                System.Runtime.InteropServices.Marshal.ZeroFreeBSTR(ptr);
            }
        }

        private async Task<SystemInfo> GetSystemInfoLegacyAsync(string computerName)
        {
            var systemInfo = new SystemInfo();

            try
            {
                // CPU-Auslastung abrufen
                systemInfo.CpuUsage = await GetCpuUsageAsync(computerName);

                // Speicher-Informationen abrufen
                var memoryInfo = await GetMemoryInfoAsync(computerName);
                systemInfo.TotalMemoryGB = memoryInfo.TotalGB;
                systemInfo.UsedMemoryGB = memoryInfo.UsedGB;

                // Festplatten-Informationen abrufen
                systemInfo.DiskPartitions = await GetDiskInfoAsync(computerName);

                return systemInfo;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Fehler beim Abrufen der Systemdaten für {computerName}: {ex.Message}");
                return systemInfo; // Leere Daten zurückgeben
            }
        }

        private async Task<int> GetCpuUsageAsync(string computerName)
        {
            try
            {
                var script = "Get-WmiObject -Class Win32_Processor | Measure-Object -Property LoadPercentage -Average | Select-Object -ExpandProperty Average";
                var result = await ExecutePowerShellAsync(script);

                if (result.Any())
                {
                    var output = result.First().Output?.ToString();
                    if (int.TryParse(output, out int cpuUsage))
                    {
                        return cpuUsage;
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"CPU-Fehler für {computerName}: {ex.Message}");
            }

            return 0; // Keine Daten verfügbar
        }

        private async Task<(double TotalGB, double UsedGB)> GetMemoryInfoAsync(string computerName)
        {
            try
            {
                var script = "Get-WmiObject -Class Win32_OperatingSystem | ForEach-Object { [PSCustomObject]@{ Total = $_.TotalVisibleMemorySize; Free = $_.FreePhysicalMemory } } | ConvertTo-Json";
                var result = await ExecutePowerShellAsync(script);

                if (result.Any())
                {
                    var output = result.First().Output?.ToString();
                    if (!string.IsNullOrEmpty(output))
                    {
                        // TODO: JSON-Parsing implementieren
                        // Für jetzt keine Daten zurückgeben
                        return (0, 0);
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Memory-Fehler für {computerName}: {ex.Message}");
            }

            // Keine Daten verfügbar
            return (0, 0);
        }

        private async Task<List<DiskPartition>> GetDiskInfoAsync(string computerName)
        {
            var partitions = new List<DiskPartition>();

            try
            {
                Debug.WriteLine($"[PowerShell] Versuche Festplattendaten für {computerName} abzurufen...");

                var script = "Get-WmiObject -Class Win32_LogicalDisk | Where-Object {$_.DriveType -eq 3} | ForEach-Object { \"$($_.DeviceID)|$($_.Size)|$($_.FreeSpace)|$($_.VolumeName)\" }";
                var result = await ExecutePowerShellAsync(script);

                Debug.WriteLine($"[PowerShell] PowerShell-Ergebnis für {computerName}: {result?.Count ?? 0} Einträge");

                if (result.Any())
                {
                    var output = result.First().Output?.ToString();
                    if (!string.IsNullOrEmpty(output))
                    {
                        var lines = output.Split('\n', StringSplitOptions.RemoveEmptyEntries);

                        foreach (var line in lines)
                        {
                            var parts = line.Split('|');
                            if (parts.Length >= 3)
                            {
                                var deviceId = parts[0].Trim();
                                double sizeBytes = 0;
                                double freeBytes = 0;

                                if (double.TryParse(parts[1], out sizeBytes) &&
                                    double.TryParse(parts[2], out freeBytes) &&
                                    sizeBytes > 0)
                                {
                                    var volumeName = parts.Length > 3 ? parts[3].Trim() : "";

                                    var partition = new DiskPartition
                                    {
                                        DriveLetter = deviceId.Replace(":", ""),
                                        Label = string.IsNullOrEmpty(volumeName) ? "Local Disk" : volumeName,
                                        TotalSpaceGB = sizeBytes / 1024 / 1024 / 1024,
                                        UsedSpaceGB = (sizeBytes - freeBytes) / 1024 / 1024 / 1024
                                    };

                                    partitions.Add(partition);
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Disk-Fehler für {computerName}: {ex.Message}");
            }

            // Keine Fallback-Daten - wenn keine echten Daten verfügbar sind, bleibt die Liste leer
            if (partitions.Count == 0)
            {
                Debug.WriteLine($"[PowerShell] Keine Festplattendaten für {computerName} verfügbar - Abruf nicht erfolgreich");
            }

            return partitions;
        }

        private async Task<List<dynamic>> ExecutePowerShellAsync(string script)
        {
            return await Task.Run(() =>
            {
                try
                {
                    var processInfo = new ProcessStartInfo
                    {
                        FileName = "powershell.exe",
                        Arguments = $"-Command \"{script}\"",
                        RedirectStandardOutput = true,
                        RedirectStandardError = true,
                        UseShellExecute = false,
                        CreateNoWindow = true
                    };

                    using var process = Process.Start(processInfo);
                    var output = process.StandardOutput.ReadToEnd();
                    var error = process.StandardError.ReadToEnd();

                    process.WaitForExit();

                    if (!string.IsNullOrEmpty(error))
                    {
                        System.Diagnostics.Debug.WriteLine($"PowerShell Error: {error}");
                    }

                    // Einfache Parsing-Logik für die Ausgabe
                    var results = new List<dynamic>();
                    if (!string.IsNullOrEmpty(output))
                    {
                        // Für jetzt geben wir die rohe Ausgabe zurück
                        results.Add(new { Output = output.Trim() });
                    }

                    return results;
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"PowerShell Execution Error: {ex.Message}");
                    return new List<dynamic>();
                }
            });
        }
    }

    public class SystemInfo
    {
        public int CpuUsage { get; set; }
        public double TotalMemoryGB { get; set; }
        public double UsedMemoryGB { get; set; }
        public List<DiskPartition> DiskPartitions { get; set; } = new List<DiskPartition>();
    }

    public class ServerSystemInfo
    {
        public string ComputerName { get; set; }
        public int CpuUsage { get; set; }
        public double TotalMemoryGB { get; set; }
        public double UsedMemoryGB { get; set; }
        public string OperatingSystem { get; set; }
        public DateTime LastBootTime { get; set; }
        public List<DiskPartition> DiskPartitions { get; set; } = new List<DiskPartition>();
        public bool HasPendingUpdates { get; set; }
        public bool RequiresReboot { get; set; }
        public DateTime Timestamp { get; set; }
    }

    public class PowerShellResult
    {
        public bool Error { get; set; }
        public string ErrorMessage { get; set; }
        public string ComputerName { get; set; }
        public int CpuUsage { get; set; }
        public double TotalMemoryGB { get; set; }
        public double UsedMemoryGB { get; set; }
        public double FreeMemoryGB { get; set; }
        public string OperatingSystem { get; set; }
        public DateTime LastBootTime { get; set; }
        public List<PowerShellDiskInfo> DiskPartitions { get; set; } = new List<PowerShellDiskInfo>();
        public bool HasPendingUpdates { get; set; }
        public bool RequiresReboot { get; set; }
        public DateTime Timestamp { get; set; }
    }

    public class PowerShellDiskInfo
    {
        public string DriveLetter { get; set; }
        public string Label { get; set; }
        public double TotalSpaceGB { get; set; }
        public double UsedSpaceGB { get; set; }
        public double FreeSpaceGB { get; set; }
        public double UsagePercent { get; set; }
    }
}
